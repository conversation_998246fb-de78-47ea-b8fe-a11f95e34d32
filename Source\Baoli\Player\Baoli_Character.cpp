// Fill out your copyright notice in the Description page of Project Settings.


#include "Baoli/Player/Baoli_Character.h"

#include "KismetAnimationLibrary.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "EnhancedInputComponent.h"
#include "Components/SpotLightComponent.h"
#include "Kismet/KismetMathLibrary.h"

#include "Baoli_Controller.h"

// Sets default values
ABaoli_Character::ABaoli_Character()
{
 	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	//Capsule Component
	GetCapsuleComponent()->SetCapsuleRadius(50.0f);
	GetCapsuleComponent()->SetCapsuleHalfHeight(86.0f);

	//Skeletal Mesh
	GetMesh()->SetupAttachment(GetCapsuleComponent());

	// Initialize PlayerController to nullptr to avoid crashes
	PlayerController = nullptr;

	//Spring Arm
	SpringArmComponent = CreateDefaultSubobject<USpringArmComponent>(TEXT("SpringArm"));
	SpringArmComponent->SetupAttachment(GetCapsuleComponent());
	SpringArmComponent->TargetArmLength = 300.0f;
	SpringArmComponent->bUsePawnControlRotation = true;
	SpringArmComponent->bDoCollisionTest = true;

	//Camera
	CameraComponent = CreateDefaultSubobject<UCameraComponent>(TEXT("Camera"));
	CameraComponent->SetupAttachment(SpringArmComponent);
	CameraComponent->FieldOfView = FOV;

	//Inspection Light
	InspectionLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("Inspection Light"));
	InspectionLightComponent->SetupAttachment(GetMesh());
	InspectionLightComponent->Intensity = InspectionLightIntensity;
	InspectionLightComponent->AttenuationRadius = InspectionAttenuationRadius;

	//Arrow Component
	//GetArrowComponent()->ArrowColor = FColor(FLinearColor::Blue.ToFColor(true));

	//Components
	MotionWarpingComponent = CreateDefaultSubobject<UMotionWarpingComponent>(TEXT("MotionWarpingComponent"));
}

// Called when the game starts or when spawned
void ABaoli_Character::BeginPlay()
{
	Super::BeginPlay();

	// Initialize PlayerController safely in BeginPlay
	PlayerController = Cast<ABaoli_Controller>(GetController());
	CharacterState = PlayerController->GetCharacterStatePtr();

	// Initialize movement detection state
	const float InitialSpeed = GetVelocity().Size();
	const bool bInitialOnGround = MovementComponent->IsMovingOnGround();
	bWasMovingLastFrame = bInitialOnGround && InitialSpeed > 5.0f;
	MovementStartFrameCount = 0;
}

// Called every frame
void ABaoli_Character::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	//Gait and Movement Speed update
	UpdateMovement();

	//Update Rotation Modes
	UpdateRotation();

	//Update Movement Start Detection
	UpdateMovementStartDetection(DeltaTime);

}

// Called to bind functionality to input
void ABaoli_Character::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	// Initialize PlayerController if it's null (since SetupPlayerInputComponent can be called before BeginPlay)
	if (PlayerController == nullptr)
	{
		PlayerController = Cast<ABaoli_Controller>(GetController());
		if (PlayerController == nullptr)
		{
			UE_LOG(LogTemp, Warning, TEXT("PlayerController is null in SetupPlayerInputComponent"));
			return; // Exit early if we still can't get a valid PlayerController
		}
	}

	UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer());
	if (Subsystem)
	{
		Subsystem->ClearAllMappings();

		if (InputMapping)
		{
			Subsystem->AddMappingContext(InputMapping, 0);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("InputMapping is null in SetupPlayerInputComponent"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Enhanced Input Subsystem is null in SetupPlayerInputComponent"));
	}

	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent))
	{
		// Bind actions with null checks

		if (LookAction)
		{
			//Look Binding
			EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ABaoli_Character::Look);
		}

		if (WalkAction)
		{
			//Walk Binding
			EnhancedInputComponent->BindAction(WalkAction, ETriggerEvent::Started, this, &ABaoli_Character::Walk);
		}

		if (SprintAction)
		{
			//Sprint Binding
			EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Started, this, &ABaoli_Character::Sprint);
		}

		if (StrafeAction)
		{
			//Strafe Binding
			EnhancedInputComponent->BindAction(StrafeAction, ETriggerEvent::Triggered, this, &ABaoli_Character::Strafe);
		}

		if (AimAction)
		{
			//Aim Binding
			EnhancedInputComponent->BindAction(AimAction, ETriggerEvent::Triggered, this, &ABaoli_Character::Aim);
		}

		if (CrouchAction)
		{
			//Crouch Binding
			EnhancedInputComponent->BindAction(CrouchAction, ETriggerEvent::Triggered, this, &ABaoli_Character::MyCrouch);
		}

		if (JumpAction)
		{
			//Jump Binding
			EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Triggered, this, &ABaoli_Character::Jumping);
		}
	}
}

//Input Functions
void ABaoli_Character::Move(const FVector2D MovementVector)
{
	// Get the control rotation (camera rotation) instead of actor rotation
	const FRotator ControlRotation = GetControlRotation();

	// Get forward and right vectors from control rotation
	const FVector ForwardDirection = FRotationMatrix(ControlRotation).GetUnitAxis(EAxis::X);
	const FVector RightDirection = FRotationMatrix(ControlRotation).GetUnitAxis(EAxis::Y);

	// Apply movement input using camera-relative directions
	AddMovementInput(ForwardDirection, MovementVector.Y);
	AddMovementInput(RightDirection, MovementVector.X);
}

void ABaoli_Character::Look(const FInputActionValue& Value)
{
	 const FVector2D LookVector = Value.Get<FVector2D>();
	

	switch (*CharacterState)
	{
	case ECharacterState::OverWorld:
		AddControllerYawInput(LookVector.X* AimSensitivity);
		AddControllerPitchInput(LookVector.Y* AimSensitivity);
		break;
	case ECharacterState::Mechanic:
		break;
	case ECharacterState::GameMenu:
		break;
	default:
		break;
	}
}

void ABaoli_Character::Walk()
{ // A Flip Flip node to Change the Gait
	if(!bWantsToSprint)
	{
		bWantsToSprint = true;
	}
	else
	{
		bWantsToSprint = false;
	}
}

void ABaoli_Character::Sprint(const FInputActionValue& Value)
{ // Sprint Flip
	bWantsToSprint = Value.Get<bool>();
}

void ABaoli_Character::Strafe()
{ // Make sure that Strafe is on No MATTER WHAT
	if (!bWantsToStrafe)
	{
		bWantsToStrafe = true;
	}
}

void ABaoli_Character::Aim()
{ // Right Click KeyBind doesnt stop strafe
	bWantsToStrafe = true;
}

void ABaoli_Character::MyCrouch()
{ // Crouch Gait
	if (!bIsCrouched)
	{
		Crouch();
	}
	else
	{
		UnCrouch();
	}
}

void ABaoli_Character::Jumping()
{ // Jump Gait
	if (MovementComponent->IsMovingOnGround())
	{
		Jump();
	}
}


EMyGait ABaoli_Character::getDesiredGait()
{ // Calculating Gait
	EMyGait InitialGait;
	if (bWantsToSprint)
	{
		InitialGait = EMyGait::Run;
	}
	else
	{
		InitialGait = EMyGait::Walk;
	}
	return InitialGait;
}

float ABaoli_Character::CalcMaxSpeed()
{
	FVector PlayerVelocity = GetMovementComponent()->Velocity;
	FRotator PlayerRotation = GetActorRotation();
	float inTime = UKismetAnimationLibrary::CalculateDirection(PlayerVelocity, PlayerRotation);
	float returnfloat = 0.0f;
	if (SpeedMapCurve == nullptr)
	{
		UKismetSystemLibrary::PrintString(GetWorld(),FString("SpeedMap Not Selected!"));
	}
	else
	{
		float StrafeSpeedMap = SpeedMapCurve->GetFloatValue(UKismetMathLibrary::Abs(inTime));
		FVector returnSwitch = FVector(0, 0, 0);
		if (MovementComponent->IsCrouching())
		{
			returnSwitch = FVector(200,175,150);
		}
		else
		{
			switch (CurrentGait)
			{
			case EMyGait::Walk :
				returnSwitch = FVector(200,175,150);  // Walk: forward, strafe, backward
				break;
			case EMyGait::Run :
				returnSwitch = FVector(500,350,300);  // Run: forward, strafe, backward
				break;
			case EMyGait::Sprint :
				returnSwitch = FVector(700,700,700);  // Sprint: forward, strafe, backward
				break;
			}
		}
		if (StrafeSpeedMap < 1.0)
		{
			returnfloat = UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 0.0, 1.0, returnSwitch.X, returnSwitch.Y);
		}
		else
		{
			returnfloat = UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 1.0, 2.0, returnSwitch.Y, returnSwitch.Z);
		}
	}
	return returnfloat;
}

void ABaoli_Character::UpdateMovement()
{
	CurrentGait = getDesiredGait();


	float speed = CalcMaxSpeed();
	MovementComponent->MaxWalkSpeed = speed;
	MovementComponent->MaxWalkSpeedCrouched = speed;
}
void ABaoli_Character::UpdateRotation()
{
	if (bWantsToStrafe)
	{
		MovementComponent->bUseControllerDesiredRotation = true;

		MovementComponent->bOrientRotationToMovement = false;
	}
	else
	{
		MovementComponent->bUseControllerDesiredRotation = false;

		MovementComponent->bOrientRotationToMovement = true;
	}

	// Set rotation rate regardless of falling state
	MovementComponent->RotationRate = FRotator(0,0,-1);
}

void ABaoli_Character::DisableCharacter(bool bDisableInput, bool bDisableMesh, bool bDisableCursor, bool bAnimationCamera, bool bDisableSkeletonUpdate)
{
	if(bDisableInput && PlayerController) // Disabling Input
	{
		DisableInput(PlayerController);
		UE_LOG(LogTemp, Warning, TEXT("Inputs Disabled"));
	}

	if (bDisableMesh) // Hiding Mesh
	{
		GetMesh()->SetHiddenInGame(true, true); //Hiding Mesh and all its Children
	}

	if (bDisableCursor && PlayerController)
	{
		PlayerController->SetShowMouseCursor(false);
	}

	if (bAnimationCamera)
	{
		CameraComponent->bUsePawnControlRotation = false;
	}

	if (bDisableSkeletonUpdate)
	{
		GetMesh()->bNoSkeletonUpdate = false;
	}
}

void ABaoli_Character::EnableCharacter(bool bEnableInput, bool bEnableMesh, bool bEnableCursor, bool bPlayerCamera, bool bEnableSkeletonUpdate)
{
	if(bEnableInput && PlayerController) // Enabling Input
	{
		EnableInput(PlayerController);
		UE_LOG(LogTemp, Warning, TEXT("Inputs Enabled"));
	}

	if (bEnableMesh) // UN-Hiding Mesh
	{
		GetMesh()->SetHiddenInGame(false, true); //UN-Hiding Mesh and all its Children
	}

	if (bEnableCursor && PlayerController)
	{
		PlayerController->SetShowMouseCursor(true);
	}

	if (bPlayerCamera)
	{
		CameraComponent->bUsePawnControlRotation = true;
	}

	if (bEnableSkeletonUpdate)
	{
		GetMesh()->bNoSkeletonUpdate = true;
	}
}

void ABaoli_Character::UpdateMovementStartDetection(float DeltaTime)
{
	// Check if character is moving using velocity and ground state
	const float CurrentSpeed = GetVelocity().Size();
	const bool bIsOnGround = MovementComponent->IsMovingOnGround();
	const bool bIsCurrentlyMoving = bIsOnGround && CurrentSpeed > 5.0f; // Small threshold to avoid jitter

	// If character just started moving, start the window
	if (bIsCurrentlyMoving && !bWasMovingLastFrame)
	{
		bIsInMovementStartWindow = true;
		MovementStartFrameCount = 0; // Reset counter
	}

	// If we're in the window, check conditions
	if (bIsInMovementStartWindow)
	{
		// If character stopped moving, end the window immediately
		if (!bIsCurrentlyMoving)
		{
			bIsInMovementStartWindow = false;
			MovementStartFrameCount = 0;
		}
		else
		{
			// Character is still moving, increment frame count
			MovementStartFrameCount++;

			// If we've reached the frame limit, end the window
			if (MovementStartFrameCount >= MovementStartWindowFrames)
			{
				bIsInMovementStartWindow = false;
				MovementStartFrameCount = 0;
			}
		}
	}

	// Update previous frame state
	bWasMovingLastFrame = bIsCurrentlyMoving;
}

bool ABaoli_Character::IsInMovementStartWindow() const
{
	return bIsInMovementStartWindow;
}



